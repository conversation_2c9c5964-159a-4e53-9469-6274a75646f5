---
type: "always_apply"
---

-
-项目使用的是typescript的setup语法糖编写
-所有vue3组件都先写结构然后是脚本然后是样式
-根据需求修改代码不要出现脚本或者样式以及结构错乱
-可以使用elementui plus组件并且已经全局自动引入配置好了不需要页面单独引入
-字体可以使用fontawesome项目已经集成引入直接使用即记得在plugin中引入
-创建元素的时候都要考虑加入动画项目已经集成了animate.css并正确全局配置直接使用即可
-动画还可以选择vue的motion也已经集成并且全局配置好了直接使用即可
-布局一定要精美紧凑并且使用栅格布局和媒体查询适配移动端这是非常重要的
-css使用scss编译
-必须使用栅格系统和媒体查询适配移动端
-已经全局配置echarts图表直接使用即可
-所有的组件都应适配elementui plus 暗黑模式
-项目启动命令是pnpm dev
-每次我说重构或者深度优化界面的时候你都可以联网搜索然后根据自己的想法去修改UI但是必须确保好看
-所有的消息提示 import { message } from "@/utils/message";  用这个就行  已经封装好了 不要使用elementui plus的原生提示
-fontawesome字体都需要再fontawesome.ts中加入字体因为不是全局挂载
-跟后端交互时所有的参数都是下划线的形式而不是驼峰
-表格默认从服务器获取100条 然后做本地分页 默认本地每页5条 有 10 20 30 的选项 等本地分页好了再从服务器获取新的页数据
-页面的设计开发都必须是紧凑精致的布局不要出现太大的卡片或者无所谓的阴影或者框线可以存在但是不能太多影响整个界面的美观
-当我说重制UI或者类似让你重新设计UI的时候你就自己根据现在的页面进行网络搜索

-所有的请求都用中文回答我