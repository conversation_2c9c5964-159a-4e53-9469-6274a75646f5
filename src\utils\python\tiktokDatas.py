import requests
import json
import time
import pandas as pd
import re
from urllib.parse import quote, unquote

class DouYinSearchSpider:
    def __init__(self):
        """初始化抖音搜索爬虫"""
        self.search_url = "https://www.douyin.com/aweme/v1/web/discover/search/"  # 注意URL末尾有斜杠
        
        # 基础请求头，最小化必要的头信息
        self.headers = {
            'authority': 'www.douyin.com',
            'accept': 'application/json, text/plain, */*',
            'accept-encoding': 'gzip, deflate, br',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'referer': 'https://www.douyin.com/search/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }
        
        # 最小化必要的Cookie
        self.cookies = ''
        
        # 必要的追踪参数
        self.webid = ''
        self.msToken = ''
        self.a_bogus = ''
        self.uifid = ''

    def extract_cookie_from_curl(self, curl_command):
        """
        从cURL命令中提取Cookie
        :param curl_command: 从浏览器中复制的cURL命令
        :return: 提取的Cookie字符串
        """
        cookie_match = re.search(r"cookie: (['\"])(.*?)(\1)", curl_command)
        if cookie_match:
            return cookie_match.group(2)
            
        # 尝试其他格式
        cookie_match = re.search(r"-H 'cookie: (.*?)'", curl_command)
        if cookie_match:
            return cookie_match.group(1)
            
        # 尝试双引号格式
        cookie_match = re.search(r'-H "cookie: (.*?)"', curl_command)
        if cookie_match:
            return cookie_match.group(1)
            
        # Windows命令提示符格式
        cookie_match = re.search(r'--header="cookie: (.*?)"', curl_command)
        if cookie_match:
            return cookie_match.group(1)
            
        return None

    def extract_params_from_curl(self, curl_command):
        """
        从cURL命令中提取关键参数
        :param curl_command: 从浏览器中复制的cURL命令
        :return: 提取的参数字典
        """
        params = {}
        
        # 提取webid
        webid_match = re.search(r'webid=([^&\s]+)', curl_command)
        if webid_match:
            params['webid'] = webid_match.group(1)
            
        # 提取msToken
        mstoken_match = re.search(r'msToken=([^&\s]+)', curl_command)
        if mstoken_match:
            params['msToken'] = unquote(mstoken_match.group(1))
            
        # 提取a_bogus
        bogus_match = re.search(r'a_bogus=([^&\s]+)', curl_command)
        if bogus_match:
            params['a_bogus'] = unquote(bogus_match.group(1))
            
        # 提取uifid
        uifid_match = re.search(r'uifid=([^&\s]+)', curl_command)
        if uifid_match:
            params['uifid'] = uifid_match.group(1)
            
        return params
    
    def setup_from_curl(self, curl_command):
        """
        从cURL命令设置所有必要的参数
        :param curl_command: 从浏览器中复制的cURL命令
        """
        # 提取Cookie
        cookie = self.extract_cookie_from_curl(curl_command)
        if cookie:
            self.cookies = cookie
            self.headers['cookie'] = cookie
            print("成功提取Cookie")
        else:
            print("未能从cURL命令中提取Cookie")
            
        # 提取其他参数
        params = self.extract_params_from_curl(curl_command)
        if 'webid' in params:
            self.webid = params['webid']
            print(f"成功提取webid: {self.webid[:10]}...")
        if 'msToken' in params:
            self.msToken = params['msToken']
            print(f"成功提取msToken: {self.msToken[:10]}...")
        if 'a_bogus' in params:
            self.a_bogus = params['a_bogus']
            print(f"成功提取a_bogus: {self.a_bogus[:10]}...")
        if 'uifid' in params:
            self.uifid = params['uifid']
            print(f"成功提取uifid: {self.uifid[:10]}...")
            self.headers['uifid'] = params['uifid']
    
    def search_creator(self, keyword, offset=0, count=12):
        """
        通过关键词搜索抖音达人
        :param keyword: 搜索关键词
        :param offset: 分页偏移量
        :param count: 每页数量
        :return: 搜索结果
        """
        # 基本参数
        params = {
            'device_platform': 'webapp',
            'aid': '6383',
            'channel': 'channel_pc_web',
            'search_channel': 'aweme_user_web',
            'keyword': keyword,
            'search_source': 'normal_search',
            'query_correct_type': '1',
            'is_filter_search': '0',
            'from_group_id': '',
            'offset': offset,
            'count': count,
            'pc_client_type': '1',
            'version_code': '170400',
            'version_name': '17.4.0',
            'cookie_enabled': 'true',
            'platform': 'PC',
            'downlink': '10'
        }
        
        # 添加可选的安全参数（如果有）
        optional_params = {}
        if self.webid:
            optional_params['webid'] = self.webid
        if self.msToken:
            optional_params['msToken'] = self.msToken
        if self.a_bogus:
            optional_params['a_bogus'] = self.a_bogus
        if self.uifid:
            optional_params['uifid'] = self.uifid
            
        # 合并所有参数
        params.update(optional_params)
        
        try:
            print(f"正在搜索关键词: '{keyword}', 偏移量: {offset}")
            
            # 尝试不同的请求方法
            response = None
            error_message = ""
            
            # 方法1：标准GET请求
            try:
                response = requests.get(self.search_url, params=params, headers=self.headers, timeout=10)
                print(f"方法1状态码: {response.status_code}")
            except Exception as e:
                error_message += f"方法1异常: {str(e)}\n"
                
            # 如果方法1失败，尝试方法2：增加更多浏览器参数
            if response is None or response.status_code != 200:
                try:
                    extra_params = {
                        'need_filter_settings': '1',
                        'list_type': 'multi',
                        'pc_libra_divert': 'Windows',
                        'browser_language': 'zh-CN',
                        'browser_platform': 'Win32',
                        'browser_name': 'Chrome',
                        'browser_version': '*********',
                        'browser_online': 'true'
                    }
                    params.update(extra_params)
                    response = requests.get(self.search_url, params=params, headers=self.headers, timeout=10)
                    print(f"方法2状态码: {response.status_code}")
                except Exception as e:
                    error_message += f"方法2异常: {str(e)}\n"
            
            # 如果前两种方法都失败，尝试方法3：使用session并添加更多头信息
            if response is None or response.status_code != 200:
                try:
                    session = requests.Session()
                    session.headers.update(self.headers)
                    session.headers.update({
                        'host': 'www.douyin.com',
                        'origin': 'https://www.douyin.com',
                        'priority': 'u=1, i'
                    })
                    response = session.get(self.search_url, params=params, timeout=10)
                    print(f"方法3状态码: {response.status_code}")
                except Exception as e:
                    error_message += f"方法3异常: {str(e)}\n"
            
            # 如果所有方法都失败
            if response is None:
                print(f"所有请求方法均失败:\n{error_message}")
                return None
                
            # 处理响应
            print(f"最终响应状态码: {response.status_code}")
            print(f"响应内容前200字符: {response.text[:200]}...")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    return data
                except json.JSONDecodeError:
                    print("返回的数据不是有效的JSON格式")
                    # 保存响应内容到文件以便分析
                    with open(f"error_response_{int(time.time())}.html", "w", encoding="utf-8") as f:
                        f.write(response.text)
                    print(f"响应内容已保存到文件")
                    return None
            else:
                print(f"请求失败，状态码: {response.status_code}")
                # 保存失败响应
                with open(f"error_response_{response.status_code}_{int(time.time())}.html", "w", encoding="utf-8") as f:
                    f.write(response.text)
                print(f"错误响应已保存到文件")
                return None
        except Exception as e:
            print(f"请求异常: {str(e)}")
            return None
    
    def parse_user_data(self, data):
        """
        解析用户数据
        :param data: 搜索返回的数据
        :return: 用户列表
        """
        if not data:
            print("没有获取到数据")
            return []
            
        # 输出完整响应，用于分析
        print("完整响应数据结构：")
        print(json.dumps(data, indent=2, ensure_ascii=False)[:1000] + "...")
            
        # 检查数据结构
        if 'user_list' not in data:
            print("数据结构中没有user_list字段")
            # 探测数据结构
            print("可用的顶级键：", list(data.keys()))
            return []
        
        user_list = data.get('user_list', [])
        if not user_list:
            print("用户列表为空")
            return []
        
        users = []
        print(f"\n成功获取到 {len(user_list)} 个用户数据，详情如下:\n")
        print("=" * 80)
        
        for user_item in user_list:
            try:
                if 'user_info' not in user_item:
                    print(f"警告: user_item中无user_info字段，可用键: {list(user_item.keys())}")
                    continue
                
                user_info = user_item['user_info']
                
                # 获取用户基本信息
                uid = user_info.get('uid', '')
                nickname = user_info.get('nickname', '')
                unique_id = user_info.get('unique_id', '')
                sec_uid = user_info.get('sec_uid', '')
                signature = user_info.get('signature', '')
                
                # 获取用户统计信息
                follower_count = user_info.get('follower_count', 0)  # 粉丝数
                total_favorited = user_info.get('total_favorited', 0)  # 获赞数
                following_count = user_info.get('following_count', 0)  # 关注数
                aweme_count = user_info.get('aweme_count', 0)  # 作品数
                
                # 获取用户标签和认证信息
                custom_verify = user_info.get('custom_verify', '')  # 认证信息
                
                # 如果有企业认证信息
                enterprise_verify_reason = user_info.get('enterprise_verify_reason', '')
                
                # 用户链接
                user_url = f"https://www.douyin.com/user/{sec_uid}"
                
                # 用户头像
                avatar = ""
                if 'avatar_thumb' in user_info and 'url_list' in user_info['avatar_thumb'] and user_info['avatar_thumb']['url_list']:
                    avatar = user_info['avatar_thumb']['url_list'][0]
                
                user_data = {
                    '用户ID': uid,
                    '昵称': nickname,
                    '抖音号': unique_id if unique_id else '未设置',
                    'sec_uid': sec_uid,
                    '签名': signature,
                    '粉丝数': follower_count,
                    '获赞数': total_favorited,
                    '关注数': following_count,
                    '作品数': aweme_count,
                    '认证信息': custom_verify,
                    '企业认证': enterprise_verify_reason,
                    '用户链接': user_url,
                    '头像链接': avatar
                }
                
                # 打印用户信息
                print(f"昵称: {nickname}")
                print(f"抖音号: {unique_id if unique_id else '未设置'}")
                print(f"sec_uid: {sec_uid}")
                print(f"签名: {signature}")
                print(f"粉丝数: {follower_count}")
                print(f"获赞数: {total_favorited}")
                print(f"关注数: {following_count}")
                print(f"作品数: {aweme_count}")
                print(f"认证信息: {custom_verify}")
                if enterprise_verify_reason:
                    print(f"企业认证: {enterprise_verify_reason}")
                print(f"用户链接: {user_url}")
                print("-" * 80)
                
                users.append(user_data)
                
            except Exception as e:
                print(f"解析用户数据异常: {str(e)}")
                continue
        
        return users
        
    def save_to_csv(self, data, filename='douyin_creators.csv'):
        """
        将数据保存为CSV文件
        :param data: 数据列表
        :param filename: 输出文件名
        """
        if not data:
            print("没有数据可保存")
            return
            
        try:
            df = pd.DataFrame(data)
            df.to_csv(filename, index=False, encoding='utf-8-sig')  # 使用utf-8-sig以支持Excel正确显示中文
            print(f"数据已保存为 {filename}")
        except Exception as e:
            print(f"保存数据失败: {str(e)}")
            
    def search_all_pages(self, keyword, max_pages=5):
        """
        搜索多页数据
        :param keyword: 搜索关键词
        :param max_pages: 最大页数
        :return: 所有用户数据
        """
        all_users = []
        offset = 0
        count = 12  # 每页12条记录，与实际请求一致
        
        for page in range(1, max_pages + 1):
            print(f"\n正在获取第 {page} 页数据...")
            data = self.search_creator(keyword, offset=offset, count=count)
            
            if not data:
                print(f"获取第 {page} 页数据失败")
                break
                
            users = self.parse_user_data(data)
            if not users:
                print(f"第 {page} 页没有用户数据")
                break
                
            all_users.extend(users)
            
            # 检查是否有更多数据
            has_more = data.get('has_more', 0)
            if has_more == 1:
                # 更新offset
                offset += count
            else:
                print("没有更多数据")
                break
                
            # 延时，避免请求过于频繁
            time.sleep(2)
            
        return all_users

def main():
    """主函数"""
    print("""
=================================================================
抖音达人搜索爬虫

您可以通过两种方式使用此爬虫:

1. 输入从浏览器开发者工具中复制的cURL命令
   - 打开Chrome浏览器，访问 https://www.douyin.com/search?type=user
   - 搜索任意关键词（如"电商主播"）
   - 按F12打开开发者工具，切换到Network选项卡
   - 找到搜索请求，右键点击选择"复制" → "复制为cURL"
   - 粘贴到下方提示处

2. 直接输入搜索关键词（成功率较低）
=================================================================
    """)
    
    spider = DouYinSearchSpider()
    
    # 询问用户是否有cURL命令
    choice = input("您是否有从浏览器复制的cURL命令？(y/n): ")
    if choice.lower() == 'y':
        print("\n请粘贴完整的cURL命令 (粘贴后按回车，然后在新行输入一个点号'.'并回车结束输入):")
        curl_lines = []
        while True:
            line = input()
            if line == '.':
                break
            curl_lines.append(line)
        
        curl_command = ' '.join(curl_lines)
        if curl_command:
            spider.setup_from_curl(curl_command)
        else:
            print("未提供有效的cURL命令，将使用默认设置")
    
    # 继续进行搜索
    keyword = input("\n请输入要搜索的抖音达人关键词: ")
    if not keyword:
        print("关键词不能为空")
        return
        
    max_pages = int(input("请输入要爬取的最大页数 (默认为3): ") or "3")
    
    print("=" * 80)
    print(f"开始搜索抖音达人，关键词: {keyword}，最大页数: {max_pages}")
    print("=" * 80)
    
    # 搜索并爬取数据
    all_users = spider.search_all_pages(keyword, max_pages=max_pages)
    
    if all_users:
        print(f"\n总共获取到 {len(all_users)} 个抖音达人数据")
        
        # 保存为CSV文件
        filename = f"douyin_creators_{keyword}.csv"
        spider.save_to_csv(all_users, filename)
    else:
        print("没有获取到任何数据")
    
    print("=" * 80)
    print("搜索完成")
    print("=" * 80)

if __name__ == "__main__":
    main()
