/* 特殊标签样式 */

/* VIP标签 */
.vip-tag {
  background: linear-gradient(135deg, #ff4949 0%, #cc0000 100%) !important;
  border: 1px solid #FFD700 !important;
  color: #fff !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  box-shadow:
    0 2px 6px rgba(255, 73, 73, 0.25),
    inset 0 1px 3px rgba(255, 255, 255, 0.2),
    0 0 0 1px rgba(255, 215, 0, 0.3);
  position: relative;
  overflow: hidden;
  border-radius: 3px !important;
  padding: 0 8px !important;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  height: 22px;
  line-height: 22px;

  .Star-icon {
    font-size: 12px;
    color: #FFD700;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 12px;
    width: 12px;
  }

  /* 分类标签 */
  .category-tag {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%) !important;
    border: 1px solid rgba(110, 231, 183, 0.3) !important;
    color: #fff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow:
      0 2px 6px rgba(16, 185, 129, 0.25),
      inset 0 1px 3px rgba(255, 255, 255, 0.2),
      0 0 0 1px rgba(110, 231, 183, 0.2);
    position: relative;
    overflow: hidden;
    border-radius: 3px !important;
    padding: 0 8px !important;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    height: 22px;
    line-height: 22px;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg,
          transparent 0%,
          rgba(110, 231, 183, 0.05) 45%,
          rgba(110, 231, 183, 0.15) 50%,
          rgba(110, 231, 183, 0.05) 55%,
          transparent 100%);
      transform: rotate(45deg);
    }
  }

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 0%,
        rgba(255, 215, 0, 0.05) 45%,
        rgba(255, 215, 0, 0.15) 50%,
        rgba(255, 215, 0, 0.05) 55%,
        transparent 100%);
    transform: rotate(45deg);
  }

  /* 分类标签 */
  .category-tag {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%) !important;
    border: 1px solid rgba(110, 231, 183, 0.3) !important;
    color: #fff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow:
      0 2px 6px rgba(16, 185, 129, 0.25),
      inset 0 1px 3px rgba(255, 255, 255, 0.2),
      0 0 0 1px rgba(110, 231, 183, 0.2);
    position: relative;
    overflow: hidden;
    border-radius: 3px !important;
    padding: 0 8px !important;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    height: 22px;
    line-height: 22px;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg,
          transparent 0%,
          rgba(110, 231, 183, 0.05) 45%,
          rgba(110, 231, 183, 0.15) 50%,
          rgba(110, 231, 183, 0.05) 55%,
          transparent 100%);
      transform: rotate(45deg);
    }
  }
}

/* 分类标签 */
.category-tag {
  background: linear-gradient(135deg, #409EFF 0%, #1E90FF 100%) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  color: #fff !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow:
    0 2px 6px rgba(64, 158, 255, 0.25),
    inset 0 1px 3px rgba(255, 255, 255, 0.2),
    0 0 0 1px rgba(64, 158, 255, 0.2);
  position: relative;
  overflow: hidden;
  border-radius: 3px !important;
  padding: 0 6px !important;
  letter-spacing: 0.3px;
  display: inline-flex;
  align-items: center;
  gap: 3px;
  height: 20px;
  line-height: 20px;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 0%,
        rgba(64, 158, 255, 0.05) 45%,
        rgba(64, 158, 255, 0.15) 50%,
        rgba(64, 158, 255, 0.05) 55%,
        transparent 100%);
    transform: rotate(45deg);
  }
}

/* 超级管理员标签 */
.super-admin-tag {
  background: linear-gradient(135deg, #8B5CF6 0%, #6366F1 100%) !important;
  border: 1px solid rgba(233, 213, 255, 0.6) !important;
  color: #fff !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  box-shadow:
    0 2px 6px rgba(233, 213, 255, 0.25),
    inset 0 1px 3px rgba(255, 255, 255, 0.2),
    0 0 0 1px rgba(233, 213, 255, 0.2);
  position: relative;
  overflow: hidden;
  border-radius: 3px !important;
  padding: 0 8px !important;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  height: 22px;
  line-height: 22px;

  .Admin-icon {
    font-size: 12px;
    color: #E9D5FF;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 12px;
    width: 12px;
  }

  /* 分类标签 */
  .category-tag {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%) !important;
    border: 1px solid rgba(110, 231, 183, 0.3) !important;
    color: #fff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow:
      0 2px 6px rgba(16, 185, 129, 0.25),
      inset 0 1px 3px rgba(255, 255, 255, 0.2),
      0 0 0 1px rgba(110, 231, 183, 0.2);
    position: relative;
    overflow: hidden;
    border-radius: 3px !important;
    padding: 0 8px !important;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    height: 22px;
    line-height: 22px;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg,
          transparent 0%,
          rgba(110, 231, 183, 0.05) 45%,
          rgba(110, 231, 183, 0.15) 50%,
          rgba(110, 231, 183, 0.05) 55%,
          transparent 100%);
      transform: rotate(45deg);
    }
  }

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 0%,
        rgba(216, 180, 254, 0.05) 45%,
        rgba(216, 180, 254, 0.15) 50%,
        rgba(216, 180, 254, 0.05) 55%,
        transparent 100%);
    transform: rotate(45deg);
  }

  /* 分类标签 */
  .category-tag {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%) !important;
    border: 1px solid rgba(110, 231, 183, 0.3) !important;
    color: #fff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow:
      0 2px 6px rgba(16, 185, 129, 0.25),
      inset 0 1px 3px rgba(255, 255, 255, 0.2),
      0 0 0 1px rgba(110, 231, 183, 0.2);
    position: relative;
    overflow: hidden;
    border-radius: 3px !important;
    padding: 0 8px !important;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    height: 22px;
    line-height: 22px;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg,
          transparent 0%,
          rgba(110, 231, 183, 0.05) 45%,
          rgba(110, 231, 183, 0.15) 50%,
          rgba(110, 231, 183, 0.05) 55%,
          transparent 100%);
      transform: rotate(45deg);
    }
  }
}

/* 分类标签 */
.category-tag {
  background: linear-gradient(135deg, #409EFF 0%, #1E90FF 100%) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  color: #fff !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow:
    0 2px 6px rgba(64, 158, 255, 0.25),
    inset 0 1px 3px rgba(255, 255, 255, 0.2),
    0 0 0 1px rgba(64, 158, 255, 0.2);
  position: relative;
  overflow: hidden;
  border-radius: 3px !important;
  padding: 0 6px !important;
  letter-spacing: 0.3px;
  display: inline-flex;
  align-items: center;
  gap: 3px;
  height: 20px;
  line-height: 20px;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 0%,
        rgba(64, 158, 255, 0.05) 45%,
        rgba(64, 158, 255, 0.15) 50%,
        rgba(64, 158, 255, 0.05) 55%,
        transparent 100%);
    transform: rotate(45deg);
  }
}

/* 普通管理员标签 */
.normal-admin-tag {
  background: linear-gradient(135deg, #3B82F6 0%, #60A5FA 100%) !important;
  border: 1px solid rgba(147, 197, 253, 0.3) !important;
  color: #fff !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  box-shadow:
    0 2px 6px rgba(59, 130, 246, 0.25),
    inset 0 1px 3px rgba(255, 255, 255, 0.2),
    0 0 0 1px rgba(147, 197, 253, 0.2);
  position: relative;
  overflow: hidden;
  border-radius: 3px !important;
  padding: 0 8px !important;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  height: 22px;
  line-height: 22px;

  .Admin-icon {
    font-size: 12px;
    color: #BFDBFE;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 12px;
    width: 12px;
  }

  /* 分类标签 */
  .category-tag {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%) !important;
    border: 1px solid rgba(110, 231, 183, 0.3) !important;
    color: #fff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow:
      0 2px 6px rgba(16, 185, 129, 0.25),
      inset 0 1px 3px rgba(255, 255, 255, 0.2),
      0 0 0 1px rgba(110, 231, 183, 0.2);
    position: relative;
    overflow: hidden;
    border-radius: 3px !important;
    padding: 0 8px !important;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    height: 22px;
    line-height: 22px;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg,
          transparent 0%,
          rgba(110, 231, 183, 0.05) 45%,
          rgba(110, 231, 183, 0.15) 50%,
          rgba(110, 231, 183, 0.05) 55%,
          transparent 100%);
      transform: rotate(45deg);
    }
  }

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 0%,
        rgba(64, 158, 255, 0.05) 45%,
        rgba(64, 158, 255, 0.15) 50%,
        rgba(64, 158, 255, 0.05) 55%,
        transparent 100%);
    transform: rotate(45deg);
  }

  /* 分类标签 */
  .category-tag {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%) !important;
    border: 1px solid rgba(110, 231, 183, 0.3) !important;
    color: #fff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow:
      0 2px 6px rgba(16, 185, 129, 0.25),
      inset 0 1px 3px rgba(255, 255, 255, 0.2),
      0 0 0 1px rgba(110, 231, 183, 0.2);
    position: relative;
    overflow: hidden;
    border-radius: 3px !important;
    padding: 0 8px !important;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    height: 22px;
    line-height: 22px;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg,
          transparent 0%,
          rgba(110, 231, 183, 0.05) 45%,
          rgba(110, 231, 183, 0.15) 50%,
          rgba(110, 231, 183, 0.05) 55%,
          transparent 100%);
      transform: rotate(45deg);
    }
  }
}