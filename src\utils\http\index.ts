import Axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type CustomParamsSerializer
} from "axios";
import type {
  PureHttpError,
  RequestMethods,
  PureHttpResponse,
  PureHttpRequestConfig
} from "./types.d";
import { stringify } from "qs";
import NProgress from "../progress";
import { getToken, formatToken } from "@/utils/auth";
import { useUserStoreHook } from "@/store/modules/user";
import { message, closeAllMessage } from "@/utils/message";

// 相关配置请参考：www.axios-js.com/zh-cn/docs/#axios-request-config-1
const defaultConfig: AxiosRequestConfig = {
  // 请求超时时间
  timeout: 5000,
  headers: {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest"
  },
  // 数组格式参数序列化（https://github.com/axios/axios/issues/5142）
  paramsSerializer: {
    serialize: stringify as unknown as CustomParamsSerializer
  }
};

class PureHttp {
  constructor() {
    this.httpInterceptorsRequest();
    this.httpInterceptorsResponse();
  }

  /** `token`过期后，暂存待执行的请求 */
  private static requests = [];

  /** 防止重复刷新`token` */
  private static isRefreshing = false;

  /** 初始化配置对象 */
  private static initConfig: PureHttpRequestConfig = {};

  /** 保存当前`Axios`实例对象 */
  private static axiosInstance: AxiosInstance = Axios.create(defaultConfig);

  /** 重连原始请求 */
  private static retryOriginalRequest(config: PureHttpRequestConfig) {
    return new Promise(resolve => {
      PureHttp.requests.push((token: string) => {
        config.headers["Authorization"] = formatToken(token);
        resolve(config);
      });
    });
  }

  /** 请求拦截 */
  private httpInterceptorsRequest(): void {
    PureHttp.axiosInstance.interceptors.request.use(
      async (config: PureHttpRequestConfig): Promise<any> => {
        // 开启进度条动画
        NProgress.start();
        // 显示加载提示
        if (!config.silent) {
          // message("请稍等...", { type: "warning" });
        }

        // 调试：在控制台输出请求信息，帮助调试载荷问题
        if (process.env.NODE_ENV === 'development') {
          console.group(`🚀 HTTP Request: ${config.method?.toUpperCase()} ${config.url}`);
          console.log('📋 Headers:', config.headers);
          console.log('📦 Data:', config.data);
          console.log('🔍 Params:', config.params);
          console.groupEnd();
        }

        // 添加浏览器指纹到请求头
        const fingerprint = localStorage.getItem("browser_fingerprint");
        if (fingerprint) {
          config.headers["X-Fingerprint"] = fingerprint;
        }

        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof config.beforeRequestCallback === "function") {
          config.beforeRequestCallback(config);
          return config;
        }
        if (PureHttp.initConfig.beforeRequestCallback) {
          PureHttp.initConfig.beforeRequestCallback(config);
          return config;
        }
        /** 请求白名单，放置一些不需要`token`的接口（通过设置请求白名单，防止`token`过期后再请求造成的死循环问题） */
        const whiteList = ["/refresh-token", "/login"];
        return whiteList.some(url => config.url.endsWith(url))
          ? config
          : new Promise(resolve => {
            const data = getToken();
            if (data) {
              const now = new Date().getTime();
              const expired = parseInt(data.expires) - now <= 0;
              if (expired) {
                if (!PureHttp.isRefreshing) {
                  PureHttp.isRefreshing = true;
                  // token过期刷新
                  useUserStoreHook()
                    .handRefreshToken({ refreshToken: data.refreshToken })
                    .then(res => {
                      const token = res.data.accessToken;
                      config.headers["Authorization"] = formatToken(token);
                      PureHttp.requests.forEach(cb => cb(token));
                      PureHttp.requests = [];
                    })
                    .finally(() => {
                      PureHttp.isRefreshing = false;
                    });
                }
                resolve(PureHttp.retryOriginalRequest(config));
              } else {
                config.headers["Authorization"] = formatToken(
                  data.accessToken
                );
                resolve(config);
              }
            } else {
              resolve(config);
            }
          });
      },
      error => {
        // 请求错误时清除提示
        closeAllMessage();
        message("网络请求不存在", { type: "error" });
        return Promise.reject(error);
      }
    );
  }

  /** 响应拦截 */
  private httpInterceptorsResponse(): void {
    const instance = PureHttp.axiosInstance;
    instance.interceptors.response.use(
      (response: PureHttpResponse) => {
        // 关闭进度条动画
        NProgress.done();
        // 清除提示
        closeAllMessage();
        const $config = response.config;

        // 调试：在控制台输出响应信息
        if (process.env.NODE_ENV === 'development') {
          console.group(`✅ HTTP Response: ${$config.method?.toUpperCase()} ${$config.url}`);
          console.log('📊 Status:', response.status);
          console.log('📋 Headers:', response.headers);
          console.log('📦 Data:', response.data);
          console.groupEnd();
        }

        // 检查API返回的状态码，但不自动显示错误信息，让业务代码处理
        // 这样可以避免重复的错误提示

        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof $config.beforeResponseCallback === "function") {
          $config.beforeResponseCallback(response);
          return response.data;
        }
        if (PureHttp.initConfig.beforeResponseCallback) {
          PureHttp.initConfig.beforeResponseCallback(response);
          return response.data;
        }
        return response.data;
      },
      (error: PureHttpError) => {
        // 关闭进度条动画
        NProgress.done();
        // 清除提示
        closeAllMessage();
        const $error = error;
        $error.isCancelRequest = Axios.isCancel($error);

        // 添加超时错误提示
        if (
          $error.code === "ECONNABORTED" &&
          $error.message.includes("timeout")
        ) {
          message("网络请求超时，请检查网络连接", { type: "error" });
        } else if ($error.response) {
          // 处理HTTP错误状态码
          const status = $error.response.status;
          let errorMsg = "请求失败";

          switch (status) {
            case 400:
              errorMsg = "请求参数错误";
              break;
            case 401:
              errorMsg = "未授权，请重新登录";
              break;
            case 403:
              errorMsg = "拒绝访问";
              break;
            case 404:
              errorMsg = "请求的资源不存在";
              break;
            case 500:
              errorMsg = "服务器内部错误";
              break;
            default:
              errorMsg = `请求失败 (${status})`;
          }

          message(errorMsg, { type: "error" });
        } else {
          message("网络连接异常，请检查网络", { type: "error" });
        }

        return Promise.reject($error);
      }
    );
  }

  /** 通用请求工具函数 */
  public request<T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: PureHttpRequestConfig
  ): Promise<T> {
    const config = {
      method,
      url,
      ...param,
      ...axiosConfig
    } as PureHttpRequestConfig;

    // 单独处理自定义请求/响应回调
    return new Promise((resolve, reject) => {
      PureHttp.axiosInstance
        .request(config)
        .then((response: undefined) => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  /** 单独抽离的`post`工具函数 */
  public post<T, P>(
    url: string,
    params?: AxiosRequestConfig<P>,
    config?: PureHttpRequestConfig
  ): Promise<T> {
    return this.request<T>("post", url, params, config);
  }

  /** 单独抽离的`get`工具函数 */
  public get<T, P>(
    url: string,
    params?: AxiosRequestConfig<P>,
    config?: PureHttpRequestConfig
  ): Promise<T> {
    return this.request<T>("get", url, params, config);
  }
}

export const http = new PureHttp();
