const Layout = () => import("@/layout/index.vue");

export default {
  path: "/basic",
  name: "Basic",
  component: Layout,
  redirect: "/basic/user",
  meta: {
    icon: "ep:setting",
    title: "基础管理",
    rank: 2
  },
  children: [
    {
      path: "/basic/user",
      name: "User",
      component: () => import("@/views/basic/user.vue"),
      meta: {
        title: "用户管理",
        showLink: true
      }
    },
    {
      path: "/basic/article",
      name: "Article",
      component: () => import("@/views/basic/article.vue"),
      meta: {
        title: "文章管理",
        showLink: true
      }
    },
    {
      path: "/basic/category",
      name: "Category",
      component: () => import("@/views/basic/category.vue"),
      meta: {
        title: "分类管理",
        showLink: true
      }
    },
    {
      path: "/basic/resource",
      name: "Resource",
      component: () => import("@/views/basic/resource.vue"),
      meta: {
        title: "资源管理",
        showLink: true
      }
    },
    {
      path: "/basic/order",
      name: "Order",
      component: () => import("@/views/basic/order.vue"),
      meta: {
        title: "订单管理",
        showLink: true
      }
    },
    {
      path: "/basic/roles",
      name: "Roles",
      component: () => import("@/views/basic/roles.vue"),
      meta: {
        title: "角色管理",
        showLink: true
      }
    },
    {
      path: "/basic/payment-method",
      name: "PaymentMethod",
      component: () => import("@/views/basic/paymentMethod.vue"),
      meta: {
        title: "支付方式管理",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
