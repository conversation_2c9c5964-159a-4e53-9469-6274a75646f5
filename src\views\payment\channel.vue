<template>
  <div class="payment-channel-container">
    <!-- 添加/编辑对话框 -->
    <el-dialog 
      v-model="showAddOrEditModal" 
      :title="currentPaymentMethod ? '编辑支付渠道' : '添加支付渠道'" 
      :before-close="handleClose" 
      @closed="handleDialogClosed"
      width="800px"
    >
      <AddOrEdit 
        v-if="showAddOrEditModal" 
        :formData="currentPaymentMethod" 
        @submit-success="handleSubmitSuccess" 
      />
    </el-dialog>

    <el-card>
      <template #header>
        <el-row :gutter="10">
          <el-col :xs="24" :sm="24" :md="4" :lg="3" :xl="2">
            <span class="header-title">支付渠道管理</span>
          </el-col>
          <el-col :xs="24" :sm="12" :md="3" :lg="2" :xl="2">
            <el-input v-model="searchForm.id" placeholder="ID" clearable size="default" />
          </el-col>
          <el-col :xs="24" :sm="12" :md="4" :lg="3" :xl="3">
            <el-input v-model="searchForm.name" placeholder="支付渠道名称" clearable size="default" />
          </el-col>
          <el-col :xs="24" :sm="12" :md="4" :lg="3" :xl="3">
            <el-input v-model="searchForm.code" placeholder="支付代码" clearable size="default" />
          </el-col>
          <el-col :xs="24" :sm="12" :md="4" :lg="3" :xl="3">
            <el-select v-model="searchForm.type" placeholder="支付类型" style="width: 100%" size="default">
              <el-option label="全部" value="" />
              <el-option label="传统支付" value="1" />
              <el-option label="加密货币" value="2" />
              <el-option label="数字钱包" value="3" />
            </el-select>
          </el-col>
          <el-col :xs="24" :sm="12" :md="3" :lg="2" :xl="2">
            <el-select v-model="searchForm.status" placeholder="状态" style="width: 100%" size="default">
              <el-option label="全部" value="" />
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-col>
          <el-col :xs="24" :sm="12" :md="3" :lg="2" :xl="2">
            <el-select v-model="searchForm.is_crypto" placeholder="加密货币" style="width: 100%" size="default">
              <el-option label="全部" value="" />
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-col>
          <el-col :xs="12" :sm="6" :md="3" :lg="2" :xl="2">
            <div class="search-buttons">
              <el-button type="primary" :icon="Search" @click="search">
                搜索
              </el-button>
              <el-button type="primary" :icon="RefreshLeft" @click="resetSearchForm">
                重置
              </el-button>
            </div>
          </el-col>
        </el-row>
      </template>

      <!-- 操作按钮区域 -->
      <div class="table-header">
        <div class="table-header-left">
          <el-button type="primary" :icon="Plus" @click="handleAdd" class="animate__animated animate__fadeInLeft">
            添加支付渠道
          </el-button>
          <el-button 
            type="danger" 
            :icon="Delete" 
            @click="handleBatchDelete" 
            :disabled="selectedIds.length === 0"
            class="animate__animated animate__fadeInLeft"
          >
            批量删除
          </el-button>
        </div>
        <div class="table-header-right">
          <el-button :icon="Refresh" @click="refreshData" circle />
        </div>
      </div>

      <!-- 数据表格 -->
      <pure-table
        ref="tableRef"
        :data="tableData"
        :columns="columns"
        :pagination="pagination"
        :loading="loading"
        adaptive
        :adaptiveConfig="{ offsetBottom: 110 }"
        row-key="id"
        @selection-change="handleSelectionChange"
        @page-size-change="handleSizeChange"
        @page-current-change="handleCurrentChange"
        class="animate__animated animate__fadeInUp"
      >
        <!-- 图标列 -->
        <template #icon="{ row }">
          <div class="icon-cell">
            <FontIcon v-if="row.icon" :icon="row.icon" class="payment-icon" />
            <FontIcon v-else icon="fas fa-credit-card" class="payment-icon" />
          </div>
        </template>

        <!-- 支付类型列 -->
        <template #type="{ row }">
          <el-tag 
            :type="getTypeTagType(row.type)" 
            size="small"
            class="animate__animated animate__pulse"
          >
            {{ row.type_text }}
          </el-tag>
        </template>

        <!-- 货币信息列 -->
        <template #currency="{ row }">
          <div class="currency-cell">
            <span class="currency-symbol">{{ row.currency_symbol }}</span>
            <span class="currency-code">{{ row.currency_code }}</span>
          </div>
        </template>

        <!-- 加密货币列 -->
        <template #is_crypto="{ row }">
          <el-tag 
            :type="row.is_crypto === 1 ? 'warning' : 'info'" 
            size="small"
            class="animate__animated animate__pulse"
          >
            {{ row.is_crypto_text }}
          </el-tag>
        </template>

        <!-- 状态列 -->
        <template #status="{ row }">
          <el-switch
            v-model="row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(row)"
            :loading="row.statusLoading"
            class="animate__animated animate__pulse"
          />
        </template>

        <!-- 默认支付方式列 -->
        <template #is_default="{ row }">
          <el-tag 
            :type="row.is_default === 1 ? 'success' : 'info'" 
            size="small"
            class="animate__animated animate__pulse"
          >
            {{ row.is_default_text }}
          </el-tag>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <div class="operation-buttons">
            <el-button 
              type="primary" 
              size="small" 
              :icon="Edit" 
              @click="handleEdit(row)"
              class="animate__animated animate__pulse"
            >
              编辑
            </el-button>
            <el-button 
              v-if="row.is_default === 0"
              type="warning" 
              size="small" 
              :icon="Star" 
              @click="handleSetDefault(row)"
              class="animate__animated animate__pulse"
            >
              设为默认
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              :icon="Delete" 
              @click="handleDelete(row)"
              class="animate__animated animate__pulse"
            >
              删除
            </el-button>
          </div>
        </template>
      </pure-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { 
  Search, 
  RefreshLeft, 
  Plus, 
  Delete, 
  Refresh, 
  Edit, 
  Star 
} from "@element-plus/icons-vue";
import { 
  getPaymentMethodList, 
  deletePaymentMethod, 
  updatePaymentMethodStatus,
  setDefaultPaymentMethod,
  type PaymentMethod, 
  type PaymentMethodSearchForm 
} from "@/api/paymentMethod";
import { message } from "@/utils/message";
import { FontIcon } from "@/components/ReIcon";
import AddOrEdit from "./channel/AddOrEdit.vue";

// 扩展PaymentMethod接口以包含UI状态
interface PaymentMethodWithUI extends PaymentMethod {
  statusLoading?: boolean;
}

// 响应式数据
const tableRef = ref();
const loading = ref(false);
const showAddOrEditModal = ref(false);
const currentPaymentMethod = ref<PaymentMethod | null>(null);
const tableData = ref<PaymentMethodWithUI[]>([]);
const selectedIds = ref<number[]>([]);

// 搜索表单
const searchForm = reactive<PaymentMethodSearchForm>({
  page: 1,
  limit: 10,
  id: "",
  name: "",
  code: "",
  type: "",
  status: "",
  is_crypto: "",
  currency_code: "",
  network: "",
  is_default: ""
});

// 分页信息
const pagination = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 20, 30, 50],
  layout: "total, sizes, prev, pager, next, jumper"
});

// 表格列配置
const columns = ref([
  {
    type: "selection",
    width: 50,
    align: "center"
  },
  {
    label: "ID",
    prop: "id",
    width: 80,
    align: "center"
  },
  {
    label: "图标",
    prop: "icon",
    width: 80,
    align: "center",
    slot: "icon"
  },
  {
    label: "支付渠道名称",
    prop: "name",
    minWidth: 150,
    align: "center"
  },
  {
    label: "支付代码",
    prop: "code",
    width: 120,
    align: "center"
  },
  {
    label: "支付类型",
    prop: "type",
    width: 100,
    align: "center",
    slot: "type"
  },
  {
    label: "货币信息",
    prop: "currency",
    width: 120,
    align: "center",
    slot: "currency"
  },
  {
    label: "加密货币",
    prop: "is_crypto",
    width: 100,
    align: "center",
    slot: "is_crypto"
  },
  {
    label: "状态",
    prop: "status",
    width: 80,
    align: "center",
    slot: "status"
  },
  {
    label: "默认",
    prop: "is_default",
    width: 80,
    align: "center",
    slot: "is_default"
  },
  {
    label: "排序",
    prop: "sort_order",
    width: 80,
    align: "center"
  },
  {
    label: "创建时间",
    prop: "create_time",
    width: 160,
    align: "center"
  },
  {
    label: "操作",
    prop: "operation",
    width: 250,
    align: "center",
    slot: "operation",
    fixed: "right"
  }
]);

// 获取支付类型标签类型
const getTypeTagType = (type: number) => {
  switch (type) {
    case 1: return "primary";
    case 2: return "warning";
    case 3: return "success";
    default: return "info";
  }
};

// 获取数据
const getData = async () => {
  try {
    loading.value = true;
    const params = {
      ...searchForm,
      page: pagination.currentPage,
      limit: pagination.pageSize
    };

    const response = await getPaymentMethodList(params);
    if (response.code === 200) {
      tableData.value = response.data.list.map(item => ({
        ...item,
        statusLoading: false
      }));
      pagination.total = response.data.pagination.total;
    }
  } catch (error) {
    console.error("获取支付渠道列表失败:", error);
    message("获取支付渠道列表失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 搜索
const search = () => {
  pagination.currentPage = 1;
  getData();
};

// 重置搜索表单
const resetSearchForm = () => {
  Object.assign(searchForm, {
    page: 1,
    limit: 10,
    id: "",
    name: "",
    code: "",
    type: "",
    status: "",
    is_crypto: "",
    currency_code: "",
    network: "",
    is_default: ""
  });
  search();
};

// 刷新数据
const refreshData = () => {
  getData();
};

// 添加支付渠道
const handleAdd = () => {
  currentPaymentMethod.value = null;
  showAddOrEditModal.value = true;
};

// 编辑支付渠道
const handleEdit = (row: PaymentMethod) => {
  currentPaymentMethod.value = { ...row };
  showAddOrEditModal.value = true;
};

// 删除支付渠道
const handleDelete = async (row: PaymentMethod) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除支付渠道 "${row.name}" 吗？`,
      "删除确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const response = await deletePaymentMethod(row.id);
    if (response.code === 200) {
      message("删除成功", { type: "success" });
      getData();
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
      message("删除失败", { type: "error" });
    }
  }
};

// 批量删除
const handleBatchDelete = async () => {
  if (selectedIds.value.length === 0) {
    message("请选择要删除的支付渠道", { type: "warning" });
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedIds.value.length} 个支付渠道吗？`,
      "批量删除确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    // 批量删除
    const promises = selectedIds.value.map(id => deletePaymentMethod(id));
    await Promise.all(promises);

    message("批量删除成功", { type: "success" });
    selectedIds.value = [];
    getData();
  } catch (error) {
    if (error !== "cancel") {
      console.error("批量删除失败:", error);
      message("批量删除失败", { type: "error" });
    }
  }
};

// 状态切换
const handleStatusChange = async (row: PaymentMethodWithUI) => {
  try {
    row.statusLoading = true;
    const response = await updatePaymentMethodStatus(row.id, row.status);
    if (response.code === 200) {
      message(`${row.status === 1 ? '启用' : '禁用'}成功`, { type: "success" });
    } else {
      // 恢复原状态
      row.status = row.status === 1 ? 0 : 1;
    }
  } catch (error) {
    console.error("状态更新失败:", error);
    message("状态更新失败", { type: "error" });
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1;
  } finally {
    row.statusLoading = false;
  }
};

// 设置默认支付渠道
const handleSetDefault = async (row: PaymentMethod) => {
  try {
    await ElMessageBox.confirm(
      `确定要将 "${row.name}" 设置为默认支付渠道吗？`,
      "设置默认确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info"
      }
    );

    const response = await setDefaultPaymentMethod(row.id);
    if (response.code === 200) {
      message("设置默认支付渠道成功", { type: "success" });
      getData();
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("设置默认失败:", error);
      message("设置默认失败", { type: "error" });
    }
  }
};

// 表格选择变化
const handleSelectionChange = (selection: PaymentMethod[]) => {
  selectedIds.value = selection.map(item => item.id);
};

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  getData();
};

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  getData();
};

// 对话框关闭前
const handleClose = (done: () => void) => {
  done();
};

// 对话框关闭后
const handleDialogClosed = () => {
  currentPaymentMethod.value = null;
};

// 提交成功
const handleSubmitSuccess = () => {
  showAddOrEditModal.value = false;
  getData();
};

// 组件挂载
onMounted(() => {
  getData();
});
</script>

<style scoped lang="scss">
.payment-channel-container {
  .header-title {
    font-weight: 600;
    color: var(--el-text-color-primary);
    display: flex;
    align-items: center;
    height: 32px;
  }

  .search-buttons {
    display: flex;
    gap: 8px;

    .el-button {
      flex: 1;
    }
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 0 4px;

    &-left {
      display: flex;
      gap: 12px;
    }

    &-right {
      display: flex;
      gap: 8px;
    }
  }

  .icon-cell {
    display: flex;
    justify-content: center;
    align-items: center;

    .payment-icon {
      font-size: 18px;
      color: var(--el-color-primary);
    }
  }

  .currency-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;

    .currency-symbol {
      font-weight: 600;
      color: var(--el-color-primary);
    }

    .currency-code {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }

  .operation-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
    flex-wrap: wrap;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .table-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      &-left {
        justify-content: center;
      }

      &-right {
        justify-content: center;
      }
    }

    .operation-buttons {
      flex-direction: column;
      gap: 4px;

      .el-button {
        width: 100%;
      }
    }
  }
}

// 暗黑模式适配
:deep(.dark) {
  .header-title {
    color: var(--el-text-color-primary);
  }

  .payment-icon {
    color: var(--el-color-primary);
  }
}
</style>
