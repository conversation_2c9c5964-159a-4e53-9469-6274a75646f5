import { http } from "@/utils/http";
import { baseUrlApi } from "@/api/utils";

export type FileInfo = {
  file_id: number;
  user_id: number;
  original_name: string;
  store_name: string;
  file_path: string;
  file_size: number;
  file_type: string;
  file_extension: string;
  file_hash: string;
  hash_algorithm?: string;
  device_fingerprint?: string;
  storage_type: number;
  bucket_name?: string;
  create_time: string;
  update_time: string;
  url?: string;
  is_duplicate?: boolean;
};

export type FileStatsInfo = {
  total_count: number;
  active_count: number;
  deleted_count: number;
  total_size: number;
  total_size_format: string;
  storage_type_stats: Array<{
    storage_type: number;
    count: number;
    size: number;
    size_format: string;
  }>;
  file_type_stats: Array<{
    file_type: string;
    count: number;
    size: number;
    size_format: string;
  }>;
};

export type FileListParams = {
  page?: number;
  page_size?: number;
  original_name?: string;
  file_type?: string;
  file_extension?: string;
  user_id?: number;
  file_hash?: string;
  storage_type?: number;
  min_size?: number;
  max_size?: number;
  start_date?: string;
  end_date?: string;
  sort_field?: string;
  sort_order?: string;
  status?: 'active' | 'deleted' | 'all';
};

export type FileUploadParams = {
  files: File[];
  user_id?: number;
  storage_type?: number;
  bucket_name?: string;
  device_fingerprint?: string;
};

// 上传文件
export const uploadFile = (params: FormData) => {
  return http.request<any>("post", `${baseUrlApi}/upload/uploadFile`, {
    data: params,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// 获取文件列表
export const getFileList = (params: FileListParams) => {
  return http.request<{
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
    data: FileInfo[];
  }>("get", `${baseUrlApi}/file/list`, { params });
};

// 获取文件详情
export const getFileDetail = (fileId: number) => {
  return http.request<FileInfo>("get", `${baseUrlApi}/file/detail`, {
    params: { file_id: fileId }
  });
};

// 删除文件（软删除）
export const deleteFile = (fileId: number) => {
  return http.request<any>("delete", `${baseUrlApi}/file/delete`, {
    params: { file_id: fileId }
  });
};

// 恢复被删除的文件
export const restoreFile = (fileId: number) => {
  return http.request<any>("put", `${baseUrlApi}/file/restore`, {
    data: { file_id: fileId }
  });
};

// 永久删除文件
export const forceDeleteFile = (fileId: number, deletePhysical = false) => {
  return http.request<any>("delete", `${baseUrlApi}/file/forceDelete`, {
    params: { file_id: fileId, delete_physical: deletePhysical }
  });
};

// 批量删除文件
export const batchDeleteFiles = (fileIds: number[], isForce = false) => {
  return http.request<any>("delete", `${baseUrlApi}/file/batchDelete`, {
    data: { file_ids: fileIds, is_force: isForce }
  });
};

// 获取文件统计信息
export const getFileStats = () => {
  return http.request<FileStatsInfo>("get", `${baseUrlApi}/file/stats`);
}; 