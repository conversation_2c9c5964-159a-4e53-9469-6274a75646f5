{"eslintConfig": {"root": true, "env": {"node": true}, "extends": ["@vue/standard"], "rules": {"indent": [1, 4]}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "name": "pure-admin-thin", "version": "5.8.0", "private": true, "type": "module", "scripts": {"dev": "NODE_OPTIONS=--max-old-space-size=4096 vite", "serve": "pnpm dev", "build": "rimraf dist && NODE_OPTIONS=--max-old-space-size=8192 vite build", "build:staging": "rimraf dist && vite build --mode staging", "report": "rimraf dist && vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f . -r", "clean:cache": "rimraf .eslintcache && rimraf pnpm-lock.yaml && rimraf node_modules && pnpm store prune && pnpm install", "lint:eslint": "echo 'ESLint已禁用' && exit 0", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{html,vue,css,scss}\" --cache-location node_modules/.cache/stylelint/", "lint": "pnpm lint:prettier && pnpm lint:stylelint", "prepare": "husky", "preinstall": "npx only-allow pnpm", "socket:server": "ts-node src/socket.io/server.ts"}, "keywords": ["pure-admin-thin", "vue-pure-admin", "element-plus", "tailwindcss", "pure-admin", "typescript", "pinia", "vue3", "vite", "esm"], "homepage": "https://github.com/pure-admin/pure-admin-thin", "repository": {"type": "git", "url": "git+https://github.com/pure-admin/pure-admin-thin.git"}, "bugs": {"url": "https://github.com/pure-admin/vue-pure-admin/issues"}, "license": "MIT", "author": {"name": "xiaoxian521", "email": "<EMAIL>", "url": "https://github.com/xiaoxian521"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@pureadmin/descriptions": "^1.2.1", "@pureadmin/table": "^3.2.0", "@pureadmin/utils": "^2.4.8", "@vueuse/core": "^10.11.1", "@vueuse/motion": "^2.2.3", "animate.css": "^4.1.1", "axios": "^1.7.4", "dayjs": "^1.11.12", "echarts": "^5.5.1", "element-plus": "^2.8.0", "js-cookie": "^3.0.5", "localforage": "^1.10.0", "md-editor-v3": "^5.5.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "openai": "^4.96.0", "path": "^0.12.7", "pinia": "^2.2.2", "pinyin-pro": "^3.24.2", "qs": "^6.13.0", "responsive-storage": "^2.2.0", "sortablejs": "^1.15.2", "spark-md5": "^3.0.2", "vue": "^3.4.38", "vue-router": "^4.4.3", "vue-tippy": "^6.4.4", "vue-types": "^5.1.3"}, "devDependencies": {"@commitlint/cli": "^19.4.0", "@commitlint/config-conventional": "^19.2.2", "@commitlint/types": "^19.0.3", "@eslint/js": "^9.9.0", "@faker-js/faker": "^8.4.1", "@iconify-icons/ep": "^1.2.12", "@iconify-icons/ri": "^1.2.10", "@iconify/vue": "^4.1.2", "@pureadmin/theme": "^3.2.0", "@types/express": "^5.0.1", "@types/gradient-string": "^1.1.6", "@types/js-cookie": "^3.0.6", "@types/node": "^20.16.1", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.15", "@types/socket.io": "^3.0.2", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-vue": "^5.1.2", "@vitejs/plugin-vue-jsx": "^4.0.1", "autoprefixer": "^10.4.20", "boxen": "^7.1.1", "cssnano": "^7.0.5", "eslint": "^9.9.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "express": "^5.1.0", "gradient-string": "^2.0.2", "husky": "^9.1.4", "lint-staged": "^15.2.9", "postcss": "^8.4.41", "postcss-html": "^1.7.0", "postcss-import": "^16.1.0", "postcss-scss": "^4.0.9", "prettier": "^3.3.3", "rimraf": "^5.0.10", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.8", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "stylelint": "^16.8.2", "stylelint-config-recess-order": "^5.0.1", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-prettier": "^5.0.2", "svgo": "^3.3.2", "tailwindcss": "^3.4.10", "typescript": "^5.5.4", "vite": "^5.4.1", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-checker": "^0.7.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-fake-server": "^2.1.1", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-router-warn": "^1.0.0", "vite-plugin-vue-inspector": "^5.1.3", "vite-svg-loader": "^5.1.0", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.0.29"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0", "pnpm": ">=9"}, "pnpm": {"allowedDeprecatedVersions": {"are-we-there-yet": "*", "sourcemap-codec": "*", "domexception": "*", "w3c-hr-time": "*", "inflight": "*", "npmlog": "*", "rimraf": "*", "stable": "*", "gauge": "*", "abab": "*", "glob": "*"}, "peerDependencyRules": {"allowedVersions": {"eslint": "9"}}}}